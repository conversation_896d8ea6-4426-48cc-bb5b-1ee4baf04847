#!/usr/bin/env python3
"""
Test script for POP/SMTP tracking system functionality.
This script demonstrates how the tracking system works without running the full GMX creator.
"""

import os
import sys
import json
import logging
from datetime import datetime

# Add current directory to path to import gmx module
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import the POPSMTPTracker class from gmx.py
from gmx import POPSMTPTracker, setup_logging

def test_tracking_system():
    """Test the POP/SMTP tracking system functionality"""
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger("POPSMTPTrackingTest")
    
    logger.info("=== POP/SMTP Tracking System Test ===")
    
    # Initialize the tracker
    tracker = POPSMTPTracker(logger)
    
    # Test data - sample email addresses
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    logger.info("1. Testing account activation tracking...")
    
    # Test marking accounts as activated
    for i, email in enumerate(test_emails[:3], 1):
        additional_info = {
            "activation_method": "test_activation",
            "test_run": i,
            "browser_type": "test_browser"
        }
        tracker.mark_account_activated(email, additional_info)
        logger.info(f"   ✅ Marked {email} as activated")
    
    logger.info("\n2. Testing duplicate activation prevention...")
    
    # Test checking if accounts are already activated
    for email in test_emails:
        is_activated = tracker.is_account_activated(email)
        status = "✅ Already activated" if is_activated else "❌ Not activated"
        logger.info(f"   {email}: {status}")
    
    logger.info("\n3. Testing activation statistics...")
    
    # Get and display statistics
    stats = tracker.get_activation_statistics()
    if "error" not in stats:
        logger.info(f"   📊 Total activated accounts: {stats['total_activated_accounts']}")
        logger.info(f"   📅 Tracking started: {stats['tracking_file_created']}")
        logger.info(f"   🔄 Last updated: {stats['last_updated']}")
        
        if stats['recent_activations']:
            logger.info("   🕒 Recent activations:")
            for activation in stats['recent_activations'][:3]:  # Show first 3
                logger.info(f"      - {activation['email']} at {activation['timestamp']}")
    else:
        logger.error(f"   Error getting statistics: {stats['error']}")
    
    logger.info("\n4. Testing account removal (for testing purposes)...")
    
    # Test removing an account from tracking
    test_email_to_remove = test_emails[0]
    removed = tracker.remove_account_from_tracking(test_email_to_remove)
    if removed:
        logger.info(f"   ✅ Successfully removed {test_email_to_remove} from tracking")
    else:
        logger.warning(f"   ❌ Failed to remove {test_email_to_remove} from tracking")
    
    # Verify removal
    is_still_activated = tracker.is_account_activated(test_email_to_remove)
    status = "❌ Successfully removed" if not is_still_activated else "⚠️ Still in tracking"
    logger.info(f"   {test_email_to_remove}: {status}")
    
    logger.info("\n5. Testing file structure...")
    
    # Check if tracking file exists and has correct structure
    tracking_file = tracker.tracking_file
    if os.path.exists(tracking_file):
        logger.info(f"   ✅ Tracking file exists: {tracking_file}")
        
        try:
            with open(tracking_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            required_keys = ['activated_accounts', 'metadata']
            has_structure = all(key in data for key in required_keys)
            
            if has_structure:
                logger.info("   ✅ File has correct structure")
                logger.info(f"   📁 Activated accounts: {len(data.get('activated_accounts', []))}")
                metadata = data.get('metadata', {})
                logger.info(f"   📊 Total activations: {metadata.get('total_activations', 0)}")
            else:
                logger.warning("   ⚠️ File structure is incomplete")
                
        except Exception as e:
            logger.error(f"   ❌ Error reading tracking file: {str(e)}")
    else:
        logger.warning(f"   ⚠️ Tracking file not found: {tracking_file}")
    
    logger.info("\n=== Test completed ===")
    logger.info("The tracking system is ready for use with the GMX email creator!")

def cleanup_test_data():
    """Clean up test data (optional)"""
    logger = logging.getLogger("POPSMTPTrackingTest")
    
    response = input("\nDo you want to clean up test data? (y/n): ").lower().strip()
    if response == 'y':
        tracker = POPSMTPTracker(logger)
        tracking_file = tracker.tracking_file
        
        if os.path.exists(tracking_file):
            try:
                os.remove(tracking_file)
                logger.info(f"✅ Cleaned up test tracking file: {tracking_file}")
            except Exception as e:
                logger.error(f"❌ Error cleaning up test file: {str(e)}")
        else:
            logger.info("No test file to clean up")

if __name__ == "__main__":
    try:
        test_tracking_system()
        cleanup_test_data()
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
